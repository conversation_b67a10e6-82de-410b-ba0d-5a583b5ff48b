import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import path from "path";
import { type InsertBooking } from "@shared/schema";
import { body, validationResult } from "express-validator";
import rateLimit from "express-rate-limit";

// Rate limiting específico para formularios
const formLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 envíos por IP cada 15 minutos
  message: {
    error: 'Demasiados envíos de formulario. Intenta nuevamente en 15 minutos.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export async function registerRoutes(app: Express): Promise<Server> {
  // API prefix for all routes
  const apiPrefix = '/api';

  // Ruta para instrucciones de certificado SSL
  app.get('/cert-help', (req, res) => {
    res.sendFile(path.resolve(process.cwd(), 'client/public/cert-instructions.html'));
  });

  // Validadores para el formulario de contacto
  const contactValidators = [
    body('name')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('El nombre debe tener entre 2 y 100 caracteres')
      .escape(),
    body('email')
      .isEmail()
      .withMessage('Debe proporcionar un email válido')
      .normalizeEmail(),
    body('phone')
      .optional()
      .isMobilePhone('any')
      .withMessage('Debe proporcionar un número de teléfono válido'),
    body('subject')
      .trim()
      .isLength({ min: 5, max: 200 })
      .withMessage('El asunto debe tener entre 5 y 200 caracteres')
      .escape(),
    body('message')
      .trim()
      .isLength({ min: 10, max: 2000 })
      .withMessage('El mensaje debe tener entre 10 y 2000 caracteres')
      .escape(),
  ];

  // Contact form submission
  app.post(`${apiPrefix}/contact`, formLimiter, contactValidators, async (req: Request, res: Response) => {
    try {
      // Verificar errores de validación
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Datos de entrada inválidos',
          errors: errors.array()
        });
      }

      const { name, email, phone, subject, message } = req.body;

      // Store the contact request
      const contactData = {
        name,
        email,
        phone: phone || '',
        subject,
        message,
        createdAt: new Date()
      };

      const savedContact = await storage.createContact(contactData);

      // In a real application, here you might:
      // 1. Send an email notification
      // 2. Add to a CRM system
      // 3. Create a task in a project management tool

      return res.status(200).json({
        message: 'Mensaje enviado correctamente',
        id: savedContact.id
      });
    } catch (error) {
      console.error('Error processing contact form:', error);
      return res.status(500).json({
        message: 'Error al procesar la solicitud. Por favor intente nuevamente.'
      });
    }
  });

  // Booking request submission
  app.post(`${apiPrefix}/booking`, async (req, res) => {
    try {
      const {
        checkIn,
        checkOut,
        configurationType,
        adults,
        children,
        fullName,
        email,
        phone,
        specialRequests,
        pricePerNight,
        nightsCount,
        totalPrice
      } = req.body;

      // Validate required fields
      if (!checkIn || !checkOut || !configurationType || !fullName || !email || !phone) {
        return res.status(400).json({
          message: 'Por favor complete todos los campos requeridos'
        });
      }

      // Validate date format and ensure checkOut is after checkIn
      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);

      if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
        return res.status(400).json({ message: 'Formato de fecha inválido' });
      }

      if (checkOutDate <= checkInDate) {
        return res.status(400).json({
          message: 'La fecha de salida debe ser posterior a la fecha de llegada'
        });
      }

      // Helper function to format Date to YYYY-MM-DD string
      const formatDateToYYYYMMDD = (date: Date) => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // Store the booking
      const bookingData: InsertBooking = {
        checkIn: formatDateToYYYYMMDD(checkInDate),
        checkOut: formatDateToYYYYMMDD(checkOutDate),
        configurationType: String(configurationType),
        guests: {
          adults: Number(adults),
          children: Number(children),
        },
        contactInfo: {
          fullName: String(fullName),
          email: String(email),
          phone: String(phone),
        },
        specialRequests: specialRequests ? String(specialRequests) : '',
        pricing: {
          pricePerNight: Number(pricePerNight),
          nightsCount: Number(nightsCount),
          totalPrice: Number(totalPrice),
        },
        status: 'pending'
      };

      const savedBooking = await storage.createBooking(bookingData);

      // In a real application, here you might:
      // 1. Check actual availability
      // 2. Send confirmation emails
      // 3. Process payment or create payment link

      return res.status(200).json({
        message: 'Solicitud de reserva recibida correctamente',
        id: savedBooking.id,
        status: savedBooking.status
      });
    } catch (error) {
      console.error('Error processing booking request:', error);
      return res.status(500).json({
        message: 'Error al procesar la reserva. Por favor intente nuevamente.'
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
