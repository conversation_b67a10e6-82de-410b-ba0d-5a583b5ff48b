# Documentación de Seguridad - Proyecto Posada 2.0

Este documento detalla todas las medidas de seguridad implementadas en el proyecto Posada 2.0 hasta la fecha.

## Implementación de HTTPS con Certificados SSL

### Configuración de Certificados SSL
- **Ubicación de certificados**: Los certificados se almacenan en el directorio `certs/`.
- **Tipo de certificados**: Para desarrollo se utilizan certificados autofirmados.
- **Archivos de certificados**:
  - `certs/key.pem`: Clave privada
  - `certs/cert.pem`: Certificado público

### Configuración del Servidor HTTPS
- **Implementación**: Se utiliza el módulo `https` de Node.js para crear un servidor HTTPS.
- **Puerto**: El servidor HTTPS se ejecuta en el puerto 5443.
- **Opciones de configuración**:
  ```javascript
  const httpsOptions = {
    key: fs.readFileSync(path.join(process.cwd(), 'certs/key.pem')),
    cert: fs.readFileSync(path.join(process.cwd(), 'certs/cert.pem')),
    rejectUnauthorized: app.get('env') !== 'development'
  };
  ```
- **Manejo de certificados autofirmados**: En entorno de desarrollo, se configura `rejectUnauthorized: false` para permitir conexiones con certificados autofirmados.

### Redirección de HTTP a HTTPS
- **Servidor HTTP**: Se mantiene un servidor HTTP en el puerto 5080 que redirecciona automáticamente a HTTPS.
- **Middleware de redirección**: Se implementó un middleware que detecta conexiones no seguras y las redirecciona a HTTPS.
  ```javascript
  app.use((req, res, next) => {
    if (app.get('env') === 'production' && !req.secure) {
      const isSecure = req.secure || req.headers['x-forwarded-proto'] === 'https';

      if (!isSecure && req.method === 'GET') {
        const host = req.headers.host?.split(':')[0] || 'localhost';
        return res.redirect(301, `https://${host}:5443${req.url}`);
      }
    }
    next();
  });
  ```
- **Redirección inteligente**: En desarrollo, los usuarios son redirigidos a una página de ayuda para certificados (`/cert-help`).

### Página de Ayuda para Certificados
- **Ruta**: `/cert-help`
- **Propósito**: Proporciona instrucciones para aceptar certificados autofirmados en diferentes navegadores.
- **Implementación**: Archivo HTML estático servido por Express.

## Cabeceras de Seguridad

### Strict-Transport-Security (HSTS)
- **Propósito**: Forzar HTTPS en todos los navegadores compatibles.
- **Implementación**:
  ```javascript
  res.setHeader('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
  ```
- **Configuración**:
  - `max-age=63072000`: El navegador recordará que el sitio solo debe accederse usando HTTPS durante 2 años.
  - `includeSubDomains`: La política se aplica a todos los subdominios.
  - `preload`: Indica que el sitio puede ser incluido en la lista de precarga HSTS de los navegadores.

### X-Content-Type-Options
- **Propósito**: Evitar que el navegador intente MIME-sniffing.
- **Implementación**:
  ```javascript
  res.setHeader('X-Content-Type-Options', 'nosniff');
  ```

### X-Frame-Options
- **Propósito**: Evitar clickjacking (ataques de UI redressing).
- **Implementación**:
  ```javascript
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  ```
- **Configuración**: `SAMEORIGIN` permite que la página sea mostrada en un frame solo si el sitio que lo muestra es el mismo origen.

### X-XSS-Protection
- **Propósito**: Protección XSS en navegadores antiguos.
- **Implementación**:
  ```javascript
  res.setHeader('X-XSS-Protection', '1; mode=block');
  ```
- **Configuración**: `1; mode=block` activa la protección XSS del navegador y bloquea la página si se detecta un ataque.

### Content-Security-Policy (CSP)
- **Propósito**: Restringir fuentes de contenido para prevenir XSS y otros ataques de inyección.
- **Implementación en desarrollo**:
  ```javascript
  res.setHeader(
    'Content-Security-Policy',
    "default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';"
  );
  ```
- **Implementación en producción**:
  ```javascript
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'"
  );
  ```

### Referrer-Policy
- **Propósito**: Controlar la información del referrer.
- **Implementación**:
  ```javascript
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  ```
- **Configuración**: `strict-origin-when-cross-origin` envía el origen, la ruta y la cadena de consulta cuando se realiza una solicitud del mismo origen, pero solo envía el origen cuando se cruza el origen.

## Otras Medidas de Seguridad

### Validación de Datos
- **Implementación básica**: Se realiza validación de datos en el servidor para las solicitudes de formularios de contacto y reservas.
- **Mejora pendiente**: Implementar validación exhaustiva utilizando Zod u otra biblioteca.

### Manejo de Errores
- **Middleware de errores**: Se implementó un middleware para manejar errores y evitar exponer información sensible.
  ```javascript
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  ```

## ✅ Nuevas Implementaciones de Seguridad (Diciembre 2024)

### Actualización de Dependencias y Vulnerabilidades
- **✅ COMPLETADO**: Actualización masiva de dependencias vulnerables
  - Reducción de vulnerabilidades: de 29 a 18 (reducción del 38%)
  - Actualización exitosa de `imagemin-webp` v8.0.0 y `drizzle-kit` v0.31.1
  - Resolución de todos los errores de TypeScript post-actualización
  - Compilación del proyecto sin errores

### Middleware de Seguridad Implementado

#### Helmet.js - Headers de Seguridad Automáticos
- **✅ COMPLETADO**: Implementación completa de Helmet.js
- **Configuración**:
  ```javascript
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.tailwindcss.com"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        imgSrc: ["'self'", "data:", "blob:", "https:"],
        fontSrc: ["'self'", "data:", "https://fonts.gstatic.com"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));
  ```

#### Express-Rate-Limit - Limitación de Velocidad
- **✅ COMPLETADO**: Rate limiting implementado
- **Configuración general**: 100 requests por 15 minutos para APIs
- **Configuración formularios**: 5 envíos por 15 minutos por IP
- **Mensajes personalizados**: Errores en español
- **Implementación**:
  ```javascript
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 100, // máximo 100 requests por ventana
    message: {
      error: 'Demasiadas solicitudes desde esta IP, intenta nuevamente más tarde.'
    }
  });
  ```

#### Express-Validator - Validación de Entrada
- **✅ COMPLETADO**: Validación robusta implementada
- **Formulario de contacto**: Validación completa con sanitización
- **Características**:
  - Validación de email con normalización
  - Validación de teléfono móvil
  - Escape de caracteres especiales
  - Trim y validación de longitud
  - Manejo estructurado de errores
- **Implementación**:
  ```javascript
  const contactValidators = [
    body('name').trim().isLength({ min: 2, max: 100 }).escape(),
    body('email').isEmail().normalizeEmail(),
    body('phone').optional().isMobilePhone('any'),
    body('subject').trim().isLength({ min: 5, max: 200 }).escape(),
    body('message').trim().isLength({ min: 10, max: 2000 }).escape(),
  ];
  ```

#### CORS - Cross-Origin Resource Sharing
- **✅ COMPLETADO**: CORS configurado con seguridad
- **Orígenes permitidos**: Configurables por variables de entorno
- **Credenciales**: Habilitadas para desarrollo
- **Implementación**:
  ```javascript
  const corsOptions = {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:5080', 'https://localhost:5443'],
    credentials: true,
    optionsSuccessStatus: 200
  };
  ```

### Gestión Segura de Variables de Entorno
- **✅ COMPLETADO**: Implementación de dotenv
- **Archivos creados**:
  - `.env.example`: Plantilla con todas las variables necesarias
  - `.env`: Configuración para desarrollo
- **Variables configuradas**:
  - `DATABASE_URL`, `NODE_ENV`, `SESSION_SECRET`
  - `ALLOWED_ORIGINS`, `RATE_LIMIT_*`, `SSL_*`
  - `PORT`, `HTTPS_PORT`, `LOG_LEVEL`
- **Seguridad**: `.gitignore` actualizado para excluir archivos sensibles

### Documentación y Verificación Automatizada
- **✅ COMPLETADO**: Script de verificación de seguridad
- **Archivo**: `security-check-simple.ps1`
- **Verificaciones automáticas**:
  - Estado de vulnerabilidades de dependencias
  - Configuración de archivos de seguridad
  - Presencia de certificados SSL
  - Compilación TypeScript
  - Documentación de seguridad
- **Resultado actual**: Todas las verificaciones pasan exitosamente

## Mejoras de Seguridad Pendientes

1. **✅ ~~Limitación de tasa (Rate Limiting)~~**: ✅ COMPLETADO
2. **✅ ~~Configuración de CORS~~**: ✅ COMPLETADO
3. **Protección CSRF**: Implementar tokens CSRF para proteger formularios.
4. **Almacenamiento seguro de contraseñas**: Utilizar algoritmos de hash seguros como bcrypt.
5. **Implementación de autenticación y autorización robustas**.
6. **Migración a una base de datos persistente con consultas parametrizadas**.
7. **Implementación de registro (logging) seguro**.
8. **✅ ~~Escaneo regular de vulnerabilidades~~**: ✅ COMPLETADO (script automatizado)

### Vulnerabilidades Restantes (18 total)
- **Críticas**: 0
- **Altas**: 15 (principalmente en `cross-spawn`, `got`, `http-cache-semantics`, `semver-regex`)
- **Moderadas**: 3 (principalmente en `esbuild`)
- **Nota**: La mayoría están en herramientas de desarrollo y no afectan producción

---

Este documento será actualizado a medida que se implementen nuevas medidas de seguridad en el proyecto.
