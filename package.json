{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist && node scripts/copy-certs.js", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@fontsource/cabin": "^5.2.5", "@fontsource/playfair-display": "^5.2.5", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.76.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.8.1", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "framer-motion": "^11.18.2", "fs": "^0.0.2", "helmet": "^8.1.0", "https": "^1.0.0", "imagemin": "^9.0.1", "imagemin-webp": "^6.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "vaul": "^1.1.2", "wouter": "^3.7.0", "ws": "^8.18.2", "zod": "^3.25.20", "zod-validation-error": "^3.4.1"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.4", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.7", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.18", "@types/express": "4.17.22", "@types/express-session": "^1.18.1", "@types/helmet": "^0.0.48", "@types/node": "20.17.50", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "drizzle-kit": "^0.18.1", "esbuild": "^0.25.4", "postcss": "^8.5.3", "sharp": "^0.34.2", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "5.8.3", "vite": "^6.3.5"}, "optionalDependencies": {"bufferutil": "^4.0.9"}}