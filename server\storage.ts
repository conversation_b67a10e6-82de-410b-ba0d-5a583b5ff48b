import {
  users, type User, type InsertUser,
  contacts, type Contact, type InsertContact,
  bookings, type Booking, type InsertBooking,
  type IStorage
} from "@shared/schema";

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private contacts: Map<number, Contact>;
  private bookings: Map<number, Booking>;
  private userCurrentId: number;
  private contactCurrentId: number;
  private bookingCurrentId: number;

  constructor() {
    this.users = new Map();
    this.contacts = new Map();
    this.bookings = new Map();
    this.userCurrentId = 1;
    this.contactCurrentId = 1;
    this.bookingCurrentId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userCurrentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async createContact(insertContact: InsertContact): Promise<Contact> {
    const id = this.contactCurrentId++;
    const createdAt = new Date();

    // Ensure the created contact matches the Contact type
    const contact: Contact = {
      id,
      name: insertContact.name,
      email: insertContact.email,
      phone: insertContact.phone || null,
      subject: insertContact.subject,
      message: insertContact.message,
      createdAt
    };

    this.contacts.set(id, contact);
    return contact;
  }

  async createBooking(insertBooking: InsertBooking): Promise<Booking> {
    const id = this.bookingCurrentId++;
    const createdAt = new Date();

    // Ensure the created booking matches the Booking type
    const booking: Booking = {
      id,
      checkIn: new Date(insertBooking.checkIn).toISOString(),
      checkOut: new Date(insertBooking.checkOut).toISOString(),
      configurationType: insertBooking.configurationType,
      guests: insertBooking.guests, // guests es de tipo GuestsType, compatible con el jsonb de Booking
      contactInfo: insertBooking.contactInfo, // contactInfo es de tipo ContactInfoType, compatible
      pricing: insertBooking.pricing, // pricing es de tipo PricingType, compatible
      specialRequests: insertBooking.specialRequests || null,
      status: insertBooking.status || 'pending', // Utilizar el status proveniente de insertBooking
      createdAt
    };

    this.bookings.set(id, booking);
    return booking;
  }
}

export const storage = new MemStorage();
