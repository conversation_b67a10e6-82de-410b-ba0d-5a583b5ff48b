Estos principios son de aplicación obligatoria para el agente en todas sus intervenciones, con el objetivo de asegurar la máxima protección, estabilidad y calidad de cada proyecto.

## Prioridad Imperativa: Integridad y Seguridad del Proyecto

Regla Fundamental 1.1: La máxima prioridad de el agente en cada proyecto es garantizar en todo momento su integridad (que el proyecto funcione correctamente, sin errores críticos ni corrupción de datos) y su seguridad (protección robusta contra vulnerabilidades, accesos no autorizados y exposición de datos sensibles).

Regla Fundamental 1.2: Ninguna acción, modificación o sugerencia de el agente debe comprometer estos dos pilares fundamentales. Ante cualquier duda, se optará por la vía que mejor preserve la seguridad y la integridad.

## Ámbito de Trabajo Estricto y Aislado para Proteger el Sistema y el Proyecto

Regla Fundamental 2.1: el agente operará exclusivamente dentro de la carpeta raíz designada del proyecto activo (sea "Folder" o "(Client)").

Regla Fundamental 2.2: Está terminantemente prohibido que el agente cree, modifique o elimine archivos del sistema operativo Windows o cualquier archivo fuera del ámbito definido del proyecto.

Regla Fundamental 2.3: La instalación de nuevas herramientas, bibliotecas o dependencias necesarias para el proyecto se realizará siempre y sin excepción dentro de un entorno virtual específico para ese proyecto, previa propuesta y aprobación del usuario.

## Vigilancia Constante y Acción Proactiva en Seguridad del Código y Configuración

Regla Fundamental 3.1: el agente, actuando con su "experiencia senior", buscará activamente y propondrá soluciones para corregir vulnerabilidades de seguridad comunes (tales como riesgos de inyección, XSS, manejo inseguro de credenciales, exposición de API keys, etc.) a medida que se desarrolla el proyecto.

Regla Fundamental 3.2: Promoverá y aplicará configuraciones seguras para todos los componentes del proyecto (servidores, bases de datos, frameworks) y fomentará el uso de las mejores prácticas de codificación segura.

Regla Fundamental 3.3: Al aproximarse a etapas clave o la finalización del proyecto, el agente deberá proponer la realización de un análisis de seguridad (como SAST) para una revisión integral, operando siempre con cuidado y agregando los cambios exitosos en cambios.md.

## Desarrollo de Soluciones Robustas y Protocolo de Recuperación Obligatorio

Regla Fundamental 4.1: Todas las soluciones implementadas por el agente para resolver problemas o añadir funcionalidades deben ser estables, profesionales y asegurar la viabilidad a largo plazo del proyecto.

Regla Fundamental 4.2: Si una implementación resulta en un fallo crítico que compromete la integridad o seguridad del proyecto, el agente primero intentará una (hasta dos, según reglas detalladas) corrección automática inmediata. Si no tiene éxito o el riesgo de daño mayor es alto, se deberá imperativamente revertir la aplicación al último estado estable funcional conocido (basándose en la memoria de conversación, backups indicados por el usuario o control de versiones si está integrado), informando claramente al usuario de la acción.

## Documentación Esencial de Seguridad

Regla Fundamental 5.1: Todas las medidas de seguridad significativas implementadas, las configuraciones críticas de seguridad y los hallazgos importantes (incluyendo resultados de análisis SAST) serán registrados de forma clara y concisa en el archivo seguridad.md del proyecto.

## Gestión de Archivos y Estructura del Proyecto

Regla Fundamental 6.1: El agente tiene autorización para editar cualquier archivo que sea necesario para:
- Solucionar errores críticos
- Implementar nuevas funcionalidades requeridas
- Mejorar el rendimiento o la seguridad del sistema

Regla Fundamental 6.2: El agente debe proceder con extrema precaución al eliminar archivos y no eliminará ningún archivo a menos que:
- Sea un duplicado confirmado (verificando metadatos, contenido y referencias)
- Sea un archivo temporal que ya no cumple ninguna función

Regla Fundamental 6.3: El agente debe documentar cualquier eliminación de archivos, ya que la recuperación de archivos eliminados puede ser complicada y pedir la autorizacion pertinente.

## Scripts y Archivos Temporales

Regla Fundamental 7.1: El agente evitará crear scripts temporales a menos que sean absolutamente necesarios para:
- Facilitar el desarrollo
- Automatizar procesos complejos
- Implementar soluciones críticas

Regla Fundamental 7.2: Después de utilizar un script temporal, el agente deberá:
- Evaluar si tendrá utilidad futura para el proyecto
- Si no tiene utilidad futura, eliminarlo de la carpeta raíz o de donde lo haya generado
- Si tiene utilidad, documentarlo adecuadamente e integrarlo formalmente al proyecto

## Principio General de Organización

Regla Fundamental 8.1: La carpeta del proyecto debe contener únicamente archivos y scripts que sean parte fundamental del sistema.

Regla Fundamental 8.2: El agente debe mantener la estructura del proyecto limpia y organizada.

Regla Fundamental 8.3: El agente debe documentar todos los cambios significativos en el archivo cambios.md.

## Gestión Optimizada de Imágenes y Assets

Regla Fundamental 9.1: Para la conversión y optimización de imágenes, se debe utilizar exclusivamente el script estándar `scripts/convert-images.js`, que es el más eficiente y robusto del proyecto.

Regla Fundamental 9.2: Todas las imágenes deben convertirse a formato WebP con calidad 75-85% para optimizar el rendimiento del sitio web.

Regla Fundamental 9.3: Los nombres de archivo de imágenes deben seguir el patrón estandarizado `[nombre-atraccion]-[número].webp` sin caracteres especiales.

Regla Fundamental 9.4: El catálogo de imágenes (`image-catalog.json`) debe actualizarse automáticamente cada vez que se procesen imágenes, manteniendo la consistencia de rutas y metadatos.

Regla Fundamental 9.5: Las imágenes deben organizarse en carpetas individuales por atracción dentro de `client/src/assets/atracciones/`, siguiendo la estructura establecida.

Regla Fundamental 9.6: Se debe validar la integridad de las rutas de imágenes y manejar errores de forma robusta durante el procesamiento.

# Security
- Implementar HTTPS para todas las comunicaciones y protección contra CSRF (Cross-Site Request Forgery).
- El usuario prefiere implementaciones de seguridad paso a paso con recomendaciones específicas y validación del éxito después de cada implementación antes de proceder al siguiente paso.
- El usuario prefiere realizar auditorías de seguridad y implementar mejores prácticas antes de llevar proyectos a producción para evitar vulnerabilidades futuras.
- IMPLEMENTADO: Headers de seguridad con Helmet.js, rate limiting con express-rate-limit, validación de entrada con express-validator, CORS configurado, variables de entorno seguras con dotenv.
- IMPLEMENTADO: Reducción de vulnerabilidades de dependencias de 29 a 18, configuración SSL/TLS, redirección HTTP a HTTPS.
- PENDIENTE: Resolver 18 vulnerabilidades restantes (principalmente en imagemin-webp y herramientas de desarrollo), migrar a base de datos PostgreSQL, implementar autenticación robusta para producción.
