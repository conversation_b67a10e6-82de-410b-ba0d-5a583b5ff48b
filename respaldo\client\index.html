<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>La Posada del Oso | Cabaña en Pica, Chile | Experiencia de Lujo Rústico</title>
    <meta name="description" content="Descubre nuestra exclusiva cabaña La Posada del Oso en Pica, Chile. Disfruta de privacidad total, piscinas privadas, y naturaleza en su máxima expresión." />

    <!-- SEO Tags -->
    <meta name="keywords" content="La Posada del Oso, cabaña de lujo, Pica Chile, arriendo cabaña, experiencia premium, naturaleza, piscina privada" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://posadadeloso.cl/" />
    <meta property="og:title" content="La Posada del Oso | Cabaña en Pica | Lujo Rústico en la Naturaleza" />
    <meta property="og:description" content="Escápate a nuestra exclusiva cabaña en Pica, Chile. Experiencia de lujo en plena naturaleza con todas las comodidades." />
    <meta property="og:image" content="https://images.unsplash.com/photo-1542718610-a1d656d1884c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://posadadeloso.cl/" />
    <meta property="twitter:title" content="La Posada del Oso | Cabaña en Pica | Lujo Rústico en la Naturaleza" />
    <meta property="twitter:description" content="Escápate a nuestra exclusiva cabaña en Pica, Chile. Experiencia de lujo en plena naturaleza con todas las comodidades." />
    <meta property="twitter:image" content="https://images.unsplash.com/photo-1542718610-a1d656d1884c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" />

    <!-- Schema.org markup for Google -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "LodgingBusiness",
        "name": "La Posada del Oso",
        "description": "Cabaña de lujo con piscina privada y todas las comodidades en Pica, Chile.",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Sector La Banda, Lote 9, Sitio 13",
          "addressLocality": "Pica",
          "addressRegion": "Tarapacá",
          "addressCountry": "CL"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": "-20.4900",
          "longitude": "-69.3300"
        },
        "telephone": "+***********",
        "image": "https://images.unsplash.com/photo-1449158743715-0a90ebb6d2d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80",
        "priceRange": "$$$",
        "amenityFeature": [
          {"@type": "LocationFeatureSpecification", "name": "Piscina privada"},
          {"@type": "LocationFeatureSpecification", "name": "Quincho"},
          {"@type": "LocationFeatureSpecification", "name": "Estacionamiento privado"}
        ]
      }
    </script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,600;0,700;1,400&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
